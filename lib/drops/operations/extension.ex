defmodule Drops.Operations.Extension do
  @moduledoc """
  Behaviour for Operations extensions.

  Extensions allow adding functionality to Operations modules based on configuration.
  For example, the Ecto extension adds changeset validation, persistence, and
  Phoenix.HTML.FormData protocol support when a repo is configured.

  ## Extension Interface

  Extensions must implement the following callbacks:

  - `enabled?/1` - Determines if the extension should be loaded based on options
  - `extend_using_macro/1` - Returns quoted code to inject into the `__using__` macro
  - `extend_operation_runtime/1` - Returns quoted code for runtime operation modules

  ## Example Extension

      defmodule MyExtension do
        @behaviour Drops.Operations.Extension

        @impl true
        def enabled?(opts) do
          Keyword.has_key?(opts, :my_option)
        end

        @impl true
        def extend_using_macro(opts) do
          quote do
            # Code to inject into the main __using__ macro
          end
        end

        @impl true
        def extend_operation_runtime(opts) do
          quote do
            # Code to inject into runtime operation modules
          end
        end
      end

  ## Parameters

  - `opts` - The options passed to the Operations module

  ## Returns

  Returns `true` if the extension should be loaded, `false` otherwise.
  """
  @callback enabled?(opts :: keyword()) :: boolean()

  defmacro __using__(_opts) do
    quote do
      import Drops.Operations.Extension, only: [steps: 1]
    end
  end

  @doc """
  Returns quoted code to inject into runtime operation modules.

  This is called when creating operation modules that use a base operations
  module (runtime pattern).

  ## Parameters

  - `opts` - The merged options for the operation

  ## Returns

  Returns quoted Elixir code to be injected.
  """
  @callback extend_operation(opts :: keyword()) :: Macro.t()

  @doc """
  Defines the steps that this extension provides.

  Extensions can use this macro to define their step functions, similar to
  how Operations modules define their steps. This allows the framework to
  properly track which functions are provided by extensions for filtering
  during parent module imports.

  ## Example

      defmodule MyExtension do
        use Drops.Operations.Extension

        steps do
          [:my_step, :another_step]
        end

        def extend_operation(opts) do
          quote do
            def my_step(context) do
              # implementation
            end

            def another_step(context) do
              # implementation
            end
          end
        end
      end

  ## Parameters

  - `block` - A block that returns a list of step function names (atoms)

  ## Returns

  Stores the steps in a module attribute and defines a `steps/0` function.
  """
  defmacro steps(do: block) do
    quote do
      @steps unquote(Macro.escape(block))

      def steps, do: @steps
    end
  end

  @doc """
  Allows extensions to modify the UnitOfWork for an operation.

  This is called after the UnitOfWork is created to allow extensions
  to override specific steps in the processing pipeline.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `opts` - The options for the operation

  ## Returns

  Returns the modified UnitOfWork.
  """
  @callback extend_unit_of_work(
              uow :: Drops.Operations.UnitOfWork.t(),
              Module.t(),
              opts :: keyword()
            ) ::
              Drops.Operations.UnitOfWork.t()

  @doc """
  Returns default schema options for this extension.

  This callback allows extensions to provide default schema options
  that will be merged into opts.schema. For example, the Ecto extension
  can set cast: true by default.

  ## Parameters

  - `opts` - The current options for the operation

  ## Returns

  Returns a keyword list of schema options to be merged.
  """
  @callback schema_opts(opts :: keyword()) :: keyword()

  @optional_callbacks extend_unit_of_work: 3, schema_opts: 1

  @doc """
  Get enabled extensions based on the provided options and registered extensions.

  Extensions are enabled if they are registered and their enabled?(opts) callback returns true.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The options to check against

  ## Returns

  Returns a list of extension modules that should be enabled.
  """
  def enabled_extensions(registered_extensions, opts) do
    registered_extensions
    |> Enum.filter(fn extension -> extension.enabled?(opts) end)
    |> Enum.uniq()
  end

  @doc """
  Generate extension code for runtime operation modules.

  This function filters registered extensions to only those that are enabled
  based on their enabled?(opts) callback, then collects their extend_operation
  code and function names. The returned AST is preprocessed to group functions
  with the same name and arity together to avoid compiler warnings.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The merged options for the operation

  ## Returns

  Returns a tuple of {preprocessed_ast, function_names_list} where:
  - preprocessed_ast: Single quoted AST block with properly grouped functions
  - function_names_list: List of {name, arity} tuples for functions defined by extensions
  """
  def extend_operation(registered_extensions, opts) do
    extension_data =
      enabled_extensions(registered_extensions, opts)
      |> Enum.map(fn extension ->
        extension_code = extension.extend_operation(opts)

        # Get steps code if the extension defines steps
        steps_code =
          if function_exported?(extension, :steps, 0) do
            extension.steps()
          else
            quote do
            end
          end

        # Extract function names from both extension code and steps
        extension_function_names = extract_function_names(extension_code)
        steps_function_names = extract_function_names(steps_code)

        all_function_names = extension_function_names ++ steps_function_names

        # Combine extension code and steps code
        combined_code = [extension_code, steps_code]

        {combined_code, all_function_names}
      end)

    {extension_codes_lists, function_names_lists} = Enum.unzip(extension_data)

    # Flatten the extension codes (since each extension now returns a list)
    all_extension_codes = List.flatten(extension_codes_lists)

    # Preprocess and group the AST
    preprocessed_ast = preprocess_extension_ast(all_extension_codes)

    {preprocessed_ast, function_names_lists}
  end

  @doc """
  Preprocess extension AST to group functions with the same name and arity together.

  This function takes a list of quoted AST blocks from extensions and reorganizes
  them so that all functions with the same name and arity are grouped together,
  preventing Elixir compiler warnings about scattered function clauses.

  ## Parameters

  - `extension_codes` - List of quoted AST blocks from extensions

  ## Returns

  Returns a single quoted AST block with properly grouped functions.
  """
  def preprocess_extension_ast(extension_codes) do
    # Extract all expressions from all extension codes, preserving source order
    all_expressions_with_index =
      extension_codes
      |> Enum.with_index()
      |> Enum.flat_map(fn {code, source_index} ->
        extract_expressions_from_ast(code)
        |> Enum.with_index()
        |> Enum.map(fn {expr, expr_index} ->
          {expr, source_index, expr_index}
        end)
      end)

    # Split into function definitions and other expressions
    {function_defs_with_index, other_expressions_with_index} =
      all_expressions_with_index
      |> Enum.split_with(fn {expr, _source_index, _expr_index} ->
        is_function_def?(expr)
      end)

    # Group function definitions by signature, maintaining source order within groups
    grouped_functions =
      function_defs_with_index
      |> Enum.group_by(fn {expr, _source_index, _expr_index} ->
        extract_function_signature(expr)
      end)
      |> Enum.sort_by(fn {_signature, clauses} ->
        # Sort groups by the first occurrence of each function
        clauses
        |> Enum.map(fn {_expr, source_index, expr_index} -> {source_index, expr_index} end)
        |> Enum.min()
      end)
      |> Enum.flat_map(fn {_signature, clauses} ->
        # Within each group, sort by source order
        clauses
        |> Enum.sort_by(fn {_expr, source_index, expr_index} ->
          {source_index, expr_index}
        end)
        |> Enum.map(fn {expr, _source_index, _expr_index} -> expr end)
      end)

    # Extract other expressions and sort by original order
    other_expressions =
      other_expressions_with_index
      |> Enum.sort_by(fn {_expr, source_index, expr_index} ->
        {source_index, expr_index}
      end)
      |> Enum.map(fn {expr, _source_index, _expr_index} -> expr end)

    # Combine other expressions with grouped functions
    final_expressions = other_expressions ++ grouped_functions

    # Wrap in a quote block
    quote do
      (unquote_splicing(final_expressions))
    end
  end

  # Helper function to extract expressions from a quoted AST block
  defp extract_expressions_from_ast({:__block__, _meta, expressions}) do
    expressions
  end

  defp extract_expressions_from_ast(single_expression) do
    [single_expression]
  end

  # Helper function to check if an AST node is a function definition
  defp is_function_def?({:def, _meta, _args}), do: true
  defp is_function_def?({:defp, _meta, _args}), do: true
  defp is_function_def?(_), do: false

  # Helper function to extract function signature (name + arity) from function def AST
  defp extract_function_signature({:def, _meta, [{name, _meta2, args} | _]})
       when is_atom(name) do
    arity = if is_list(args), do: length(args), else: 0
    {name, arity}
  end

  defp extract_function_signature({:defp, _meta, [{name, _meta2, args} | _]})
       when is_atom(name) do
    arity = if is_list(args), do: length(args), else: 0
    {name, arity}
  end

  defp extract_function_signature(_), do: nil

  @doc """
  Extract function names from quoted AST.
  """
  def extract_function_names(ast) do
    {_ast, function_names} =
      Macro.prewalk(ast, [], fn
        {:def, _meta, [{name, _meta2, args} | _]} = node, acc when is_atom(name) ->
          arity = if is_list(args), do: length(args), else: 0
          {node, [{name, arity} | acc]}

        {:defp, _meta, [{name, _meta2, args} | _]} = node, acc when is_atom(name) ->
          arity = if is_list(args), do: length(args), else: 0
          {node, [{name, arity} | acc]}

        node, acc ->
          {node, acc}
      end)

    Enum.reverse(function_names)
  end

  @doc """
  Extract function names from steps AST.
  """
  def extract_step_function_names(steps_ast) do
    {_ast, function_names} =
      Macro.prewalk(steps_ast, [], fn
        {:def, _meta, [{name, _meta2, args} | _]} = node, acc when is_atom(name) ->
          arity = if is_list(args), do: length(args), else: 0
          {node, [{name, arity} | acc]}

        node, acc ->
          {node, acc}
      end)

    Enum.reverse(function_names)
  end

  @doc """
  Apply UnitOfWork extensions to modify the processing pipeline.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `registered_extensions` - List of registered extension modules
  - `opts` - The options for the operation

  ## Returns

  Returns the modified UnitOfWork with extension overrides applied.
  """
  def extend_unit_of_work(uow, mod, registered_extensions, opts) do
    enabled_extensions(registered_extensions, opts)
    |> Enum.reduce(uow, fn extension, acc_uow ->
      if function_exported?(extension, :extend_unit_of_work, 3) do
        extension.extend_unit_of_work(acc_uow, mod, opts)
      else
        acc_uow
      end
    end)
  end

  @doc """
  Merge schema options from enabled extensions.

  This function collects schema options from all enabled extensions
  and merges them with the base schema options.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The current options for the operation

  ## Returns

  Returns merged schema options.
  """
  def merge_schema_opts(registered_extensions, opts) do
    base_schema_opts = schema_opts(opts)

    extension_schema_opts =
      enabled_extensions(registered_extensions, opts)
      |> Enum.reduce([], fn extension, acc ->
        if function_exported?(extension, :schema_opts, 1) do
          extension_opts = extension.schema_opts(opts)
          Keyword.merge(acc, extension_opts)
        else
          acc
        end
      end)

    user_schema_opts = Keyword.get(opts, :schema, [])

    base_schema_opts
    |> Keyword.merge(extension_schema_opts)
    |> Keyword.merge(user_schema_opts)
  end

  @doc """
  Get base schema options based on operation type.

  ## Parameters

  - `opts` - The current options for the operation

  ## Returns

  Returns base schema options.
  """
  def schema_opts(opts) do
    case Keyword.get(opts, :type) do
      :form -> [atomize: true]
      _ -> []
    end
  end
end
