defmodule Drops.Operations.OptionsTest do
  use Drops.OperationCase, async: false

  describe "options merging" do
    test "merges parent and child options correctly" do
      defmodule Test.ParentOp do
        use Drops.Operations, type: :command, repo: Drops.TestRepo
      end

      defmodule Test.ChildOp do
        use Test.ParentOp, type: :form

        @impl true
        def execute(_context) do
          {:ok,
           %{
             parent_opts: __MODULE__.__opts__(),
             operation_type: __MODULE__.__operation_type__()
           }}
        end
      end

      {:ok, result} = Test.ChildOp.call(%{params: %{}})

      # Child should inherit repo from parent but override type
      assert Keyword.get(result.parent_opts, :repo) == Drops.TestRepo
      assert result.operation_type == :form
    end

    test "child options override parent options" do
      defmodule Test.ParentWithDefaults do
        use Drops.Operations, type: :command, custom_option: :parent_value
      end

      defmodule Test.ChildOverride do
        use Test.ParentWithDefaults, custom_option: :child_value

        @impl true
        def execute(_context) do
          {:ok, %{opts: __MODULE__.__opts__()}}
        end
      end

      {:ok, result} = Test.ChildOverride.call(%{params: %{}})

      # Child should override parent's custom_option
      assert Keyword.get(result.opts, :custom_option) == :child_value
    end
  end

  describe "Ecto extension enabled when repo provided" do
    @tag ecto_schemas: []
    operation [] do
      @impl true
      def execute(_context) do
        {:ok,
         %{
           enabled_extensions: enabled_extensions(),
           registered_extensions: registered_extensions(),
           opts: __opts__()
         }}
      end
    end

    test "Ecto extension is enabled when repo is provided", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{}})

      # Ecto extension should be enabled
      assert Drops.Operations.Extensions.Ecto in result.enabled_extensions
    end
  end

  describe "Ecto extension disabled when no repo" do
    operation [] do
      @impl true
      def execute(_context) do
        {:ok,
         %{
           enabled_extensions: enabled_extensions(),
           registered_extensions: registered_extensions(),
           opts: __opts__()
         }}
      end
    end

    test "Ecto extension is disabled when repo is not provided", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{}})

      # Ecto extension should not be enabled
      refute Drops.Operations.Extensions.Ecto in result.enabled_extensions
    end
  end

  describe "form operations get atomize by default" do
    operation type: :form do
      schema do
        %{
          optional(:name) => string()
        }
      end

      @impl true
      def execute(%{params: params}) do
        # Return the actual params so we can verify they were atomized
        {:ok, params}
      end
    end

    test "form operations get atomize: true by default", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{"name" => "Jane"}})
      # Params should be atomized - check that we have atom keys
      assert Map.has_key?(result, :name)
      assert result.name == "Jane"
    end
  end

  describe "command operations don't get atomize by default" do
    operation type: :command do
      schema do
        %{
          optional("name") => string()
        }
      end

      @impl true
      def execute(%{params: params}) do
        # Return the actual params so we can verify they weren't atomized
        {:ok, params}
      end
    end

    test "command operations don't get atomize by default", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{"name" => "Jane"}})
      # Params should not be atomized - check that we have string keys
      assert Map.has_key?(result, "name")
      assert result["name"] == "Jane"
    end
  end

  describe "Ecto extension provides cast by default" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    operation do
      schema(Test.Ecto.TestSchemas.UserSchema)

      @impl true
      def execute(%{changeset: changeset}) do
        # Return the changeset so we can verify it was created
        {:ok, changeset}
      end
    end

    test "Ecto extension provides cast: true by default", %{operation: operation} do
      {:ok, result} =
        operation.call(%{params: %{name: "Jane", email: "<EMAIL>"}})

      # Should get an Ecto.Changeset when cast: true (default)
      assert %Ecto.Changeset{} = result
      assert result.changes.name == "Jane"
    end
  end

  describe "user can override extension schema defaults" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    operation schema: [cast: false] do
      schema(Test.Ecto.TestSchemas.UserSchema)

      @impl true
      def execute(%{params: params}) do
        # Return the actual params so we can verify cast: false worked
        {:ok, params}
      end
    end

    test "user can override extension schema defaults", %{operation: operation} do
      {:ok, result} =
        operation.call(%{params: %{name: "Jane", email: "<EMAIL>"}})

      # With cast: false, we should get the original params map, not a changeset
      assert is_map(result)
      refute match?(%Ecto.Changeset{}, result)
      assert Map.has_key?(result, :name)
      assert result.name == "Jane"
    end
  end

  describe "user can override form operation defaults" do
    operation type: :form, schema: [atomize: false] do
      schema do
        %{
          optional("name") => string()
        }
      end

      @impl true
      def execute(%{params: params}) do
        # Return the actual params so we can verify atomize: false worked
        {:ok, params}
      end
    end

    test "user can override form operation defaults", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{"name" => "Jane"}})
      # With atomize: false, keys should remain as strings
      assert Map.has_key?(result, "name")
      assert result["name"] == "Jane"
    end
  end

  describe "options inheritance" do
    test "grandchild inherits from grandparent through parent" do
      defmodule Test.GrandParent do
        use Drops.Operations, custom_value: :grandparent, repo: Drops.TestRepo
      end

      defmodule Test.Parent do
        use Test.GrandParent, custom_value: :parent
      end

      defmodule Test.GrandChild do
        use Test.Parent, type: :form

        @impl true
        def execute(_context) do
          {:ok, %{opts: __MODULE__.__opts__()}}
        end
      end

      {:ok, result} = Test.GrandChild.call(%{params: %{}})

      # Should inherit repo from grandparent, custom_value from parent, and set own type
      assert Keyword.get(result.opts, :repo) == Drops.TestRepo
      assert Keyword.get(result.opts, :custom_value) == :parent
      assert Keyword.get(result.opts, :type) == :form
    end
  end
end
